import asyncio
import os
import uuid
import re
from typing import Optional, Dict, Any, Callable
from sqlalchemy.orm import Session
from urllib.parse import urlparse
import tempfile
import subprocess
import json

from models.database import Video
from services.video_service import VideoService
from utils.video_utils import get_video_metadata, generate_thumbnail
from config import Config


class DownloadService:
    def __init__(self, db: Session):
        self.db = db
        self.video_service = VideoService(db)
        self.downloads_dir = "videos"
        
    def validate_url(self, url: str) -> bool:
        """Validate if URL is supported and safe"""
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Check for supported domains (can be expanded)
            supported_domains = [
                'youtube.com', 'youtu.be', 'www.youtube.com',
                'vimeo.com', 'www.vimeo.com',
                'tiktok.com', 'www.tiktok.com',
                'instagram.com', 'www.instagram.com',
                'twitter.com', 'x.com', 'www.twitter.com'
            ]
            
            domain = parsed.netloc.lower()
            return any(domain.endswith(supported) for supported in supported_domains)
            
        except Exception:
            return False
    
    def get_video_info(self, url: str) -> Optional[Dict[str, Any]]:
        """Get video information without downloading"""
        try:
            cmd = [
                'yt-dlp',
                '--dump-json',
                '--no-download',
                '--no-warnings',
                url
            ]

            # Add cookie support if configured
            if Config.YTDLP_COOKIES_FILE and os.path.exists(Config.YTDLP_COOKIES_FILE):
                cmd.extend(['--cookies', Config.YTDLP_COOKIES_FILE])
            elif Config.YTDLP_COOKIES_FROM_BROWSER:
                cmd.extend(['--cookies-from-browser', Config.YTDLP_COOKIES_FROM_BROWSER])

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                error_msg = result.stderr.strip()
                # Parse common yt-dlp errors
                if "Video unavailable" in error_msg:
                    raise ValueError("Video is unavailable or private")
                elif "Unsupported URL" in error_msg:
                    raise ValueError("Unsupported URL or site")
                elif "Sign in to confirm your age" in error_msg:
                    raise ValueError("Age-restricted content not supported")
                elif "This video is only available for Music Premium members" in error_msg:
                    raise ValueError("Premium content not accessible")
                elif "Private video" in error_msg:
                    raise ValueError("Private video cannot be downloaded")
                elif "Video was deleted" in error_msg:
                    raise ValueError("Video has been deleted")
                elif "This post may not be comfortable for some audiences" in error_msg or "Log in for access" in error_msg:
                    raise ValueError("This content requires authentication. Please configure cookies using YTDLP_COOKIES_FILE or YTDLP_COOKIES_FROM_BROWSER environment variables. See documentation for details.")
                else:
                    raise ValueError(f"Failed to get video info: {error_msg}")

        except subprocess.TimeoutExpired:
            raise ValueError("Request timed out - video may be too large or network is slow")
        except json.JSONDecodeError:
            raise ValueError("Invalid response from video service")
        except Exception as e:
            if "ValueError" in str(type(e)):
                raise e
            raise ValueError(f"Error getting video info: {str(e)}")
    
    async def download_video_async(
        self, 
        url: str, 
        quality: str = "best",
        format_selector: str = "mp4",
        progress_callback: Optional[Callable[[int], None]] = None
    ) -> Optional[Video]:
        """Download video asynchronously and create database record"""
        
        # Validate URL
        if not self.validate_url(url):
            raise ValueError("Unsupported or invalid URL")
        
        # Get video info first
        video_info = self.get_video_info(url)
        if not video_info:
            raise ValueError("Could not retrieve video information")
        
        # Generate unique filename
        original_title = video_info.get('title', 'downloaded_video')
        # Sanitize filename
        safe_title = re.sub(r'[^\w\s-]', '', original_title)[:50]
        file_extension = '.mp4'  # Default to mp4
        unique_id = str(uuid.uuid4())
        unique_filename = f"{unique_id}{file_extension}"
        file_path = os.path.join(self.downloads_dir, unique_filename)
        
        # Create video record with download status
        video = Video(
            filename=unique_filename,
            original_filename=f"{safe_title}{file_extension}",
            title=original_title,
            file_path=file_path,
            file_size=0,  # Will be updated after download
            source_url=url,
            download_status="downloading",
            processing_status="pending"
        )
        
        self.db.add(video)
        self.db.commit()
        self.db.refresh(video)
        
        try:
            # Download video using yt-dlp
            await self._download_with_ytdlp(
                url, file_path, quality, format_selector, 
                video.id, progress_callback
            )
            
            # Update video record with file information
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                metadata = get_video_metadata(file_path)
                
                video.file_size = file_size
                video.duration = metadata.get('duration')
                video.width = metadata.get('width')
                video.height = metadata.get('height')
                video.fps = metadata.get('fps')
                video.download_status = "completed"
                video.download_progress = 100
                
                # Generate thumbnail
                try:
                    thumbnail_path = generate_thumbnail(file_path, video.id)
                    if thumbnail_path:
                        video.thumbnail_path = thumbnail_path
                except Exception as e:
                    print(f"Failed to generate thumbnail for video {video.id}: {e}")
                
                self.db.commit()
                return video
            else:
                raise Exception("Downloaded file not found")
                
        except Exception as e:
            # Update video record with error
            video.download_status = "failed"
            video.download_error = str(e)
            self.db.commit()
            
            # Clean up partial file
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
            
            raise e
    
    async def _download_with_ytdlp(
        self, 
        url: str, 
        output_path: str, 
        quality: str,
        format_selector: str,
        video_id: int,
        progress_callback: Optional[Callable[[int], None]] = None
    ):
        """Download video using yt-dlp with progress tracking"""
        
        # Create progress hook
        def progress_hook(d):
            if d['status'] == 'downloading':
                if 'total_bytes' in d and d['total_bytes']:
                    percent = int((d['downloaded_bytes'] / d['total_bytes']) * 100)
                elif '_percent_str' in d:
                    # Extract percentage from string like "50.0%"
                    percent_str = d['_percent_str'].replace('%', '')
                    try:
                        percent = int(float(percent_str))
                    except:
                        percent = 0
                else:
                    percent = 0
                
                # Update database
                video = self.db.query(Video).filter(Video.id == video_id).first()
                if video:
                    video.download_progress = percent
                    self.db.commit()
                
                # Call external callback
                if progress_callback:
                    progress_callback(percent)
        
        # Prepare yt-dlp command
        cmd = [
            'yt-dlp',
            '--format', f'{quality}[ext={format_selector}]/best[ext={format_selector}]/best',
            '--output', output_path,
            '--no-playlist',
            '--extract-flat', 'false',
            url
        ]
        
        # Run yt-dlp in executor to avoid blocking
        loop = asyncio.get_event_loop()
        
        def run_ytdlp():
            try:
                # Use yt-dlp Python API for better progress tracking
                import yt_dlp
                
                ydl_opts = {
                    'format': f'{quality}[ext={format_selector}]/best[ext={format_selector}]/best',
                    'outtmpl': output_path,
                    'noplaylist': True,
                    'progress_hooks': [progress_hook],
                }

                # Add cookie support if configured
                if Config.YTDLP_COOKIES_FILE and os.path.exists(Config.YTDLP_COOKIES_FILE):
                    ydl_opts['cookiefile'] = Config.YTDLP_COOKIES_FILE
                elif Config.YTDLP_COOKIES_FROM_BROWSER:
                    ydl_opts['cookiesfrombrowser'] = (Config.YTDLP_COOKIES_FROM_BROWSER, None, None, None)
                
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    ydl.download([url])
                    
            except Exception as e:
                raise Exception(f"Download failed: {str(e)}")
        
        await loop.run_in_executor(None, run_ytdlp)
    
    def update_download_progress(self, video_id: int, progress: int):
        """Update download progress for a video"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.download_progress = progress
            self.db.commit()
    
    def get_download_status(self, video_id: int) -> Optional[Dict[str, Any]]:
        """Get download status for a video"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if video:
            return {
                'video_id': video.id,
                'download_status': video.download_status,
                'download_progress': video.download_progress,
                'download_error': video.download_error,
                'processing_status': video.processing_status,
                'processing_progress': video.processing_progress
            }
        return None
