# Cookie Setup for Video Downloads

Some videos (especially from TikTok) require authentication to download. If you encounter errors like "This post may not be comfortable for some audiences. Log in for access", you need to configure cookies.

## Setup Instructions

### Method 1: Using Browser Cookies (Recommended)

1. Set the browser name in your `.env` file:
```bash
YTDLP_COOKIES_FROM_BROWSER=chrome
```

Supported browsers: `chrome`, `firefox`, `safari`, `edge`, `opera`, `brave`

### Method 2: Using Cookies File

1. Create the cookies directory:
```bash
mkdir -p data/cookies
```

2. Export cookies from your browser using a browser extension and save to `data/cookies/cookies.txt`

3. Configure the path in your `.env` file:
```bash
YTDLP_COOKIES_FILE=/app/cookies/cookies.txt
```

## Browser Cookie Export Instructions

### Chrome
1. Install "Get cookies.txt LOCALLY" extension
2. Navigate to the site you want to download from (e.g., TikTok)
3. Click the extension icon and export cookies
4. Save the file as `data/cookies/cookies.txt`

### Firefox
1. Install "cookies.txt" extension
2. Navigate to the site you want to download from
3. Click the extension icon and export cookies
4. Save the file as `data/cookies/cookies.txt`

### Safari
1. Install "ExportCookies" extension
2. Navigate to the site you want to download from
3. Export cookies using the extension
4. Save the file as `data/cookies/cookies.txt`

## Restart Required

After configuring cookies, restart your Docker containers:
```bash
docker-compose down
docker-compose up -d
```

## Security Note

Keep your cookies file secure and don't share it, as it contains your authentication information.
